#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优惠券自动领取脚本 - 使用示例
"""

import asyncio
from coupon_grabber import CouponGrabber

async def main():
    """使用示例"""
    
    # 示例userInfo（请替换为您从浏览器获取的真实值）
    USER_INFO = "70B12D2A2FD86152EE969F8C4D80EBC21215AAF6C924E106B3509859F66D785CAC974A36992F15A87CB422891083DDDACEBF6EE87724766C02FBD758823B48D501D4CD40E31EE105C492F0A91989D642BE7C25B4C79D2CB0AD7F19A890E266758030D05D98BF01EE4786EEE31F263378ECE8C0A731913E744A3D69D08736AD544B345AE1BB5532FC65C6BC397F580633408402782CBEB4CB45CDF426244BEBDFA3534C074429252F407FE68B0375F978EAD68C7AC8AC42CD9AF6182B57F9B5226CA785D0C21031B228CEABCC25F32836172AAFEAEA856E2DCB8ED5CD2498FCDDAE794E335EC55F1BEF266B17B4E175846DFF02AEDC20EDD903143B5FB634347D"
    
    print("🔧 这是一个使用示例文件")
    print("📝 请按照以下步骤获取您的userInfo：")
    print("1. 打开浏览器，访问优惠券H5页面")
    print("2. 按F12打开开发者工具")
    print("3. 切换到Network标签")
    print("4. 手动点击领取一次优惠券")
    print("5. 在Network中找到POST请求")
    print("6. 复制请求体中的userInfo字段值")
    print("7. 替换下面代码中的USER_INFO变量")
    print("\n" + "="*50)
    
    # 创建优惠券领取器
    grabber = CouponGrabber(USER_INFO)
    
    # 测试platSsn生成
    print("🧪 测试platSsn生成功能：")
    for i in range(3):
        plat_ssn = grabber.generate_plat_ssn()
        print(f"  示例{i+1}: {plat_ssn}")
    
    print("\n" + "="*50)
    print("⚠️  注意：上面的userInfo是示例数据，实际使用时请替换为您自己的数据")
    print("🚀 如果您已经配置好了userInfo，请运行: python coupon_grabber.py")

if __name__ == "__main__":
    asyncio.run(main())
