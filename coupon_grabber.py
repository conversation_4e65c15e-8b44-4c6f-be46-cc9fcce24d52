#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优惠券自动领取脚本
基于H5网页JavaScript代码分析实现的自动化工具
支持定时抢券功能，可设置精确的抢券时间
"""

import asyncio
import aiohttp
import json
import uuid
import time
import random
import threading
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
from logging.handlers import RotatingFileHandler
from email.utils import parsedate_to_datetime
from colorama import init, Fore, Style

# 初始化colorama用于彩色输出
init(autoreset=True)


class CouponGrabber:
    """优惠券自动领取器"""

    def __init__(self, user_info: str, log_dir: str = None, log_max_size: int = 10 * 1024 * 1024, log_backup_count: int = 5):
        """
        初始化优惠券领取器

        Args:
            user_info: 加密的用户信息字符串
            log_dir: 日志文件保存目录，默认为脚本所在目录
            log_max_size: 单个日志文件最大大小（字节），默认10MB
            log_backup_count: 日志文件备份数量，默认5个
        """
        # self.base_url = "https://mkts.chinaums.com/jdhsApi/jdhs/user/category/coupon/get/unionpay/householdAppliance"
        self.base_url = "https://mkts.chinaums.com/jdhsApi/jdhs/user/category/coupon/get/unionpay_online/3C"
        self.user_info = user_info

        # 日志配置
        self.log_dir = log_dir or os.path.dirname(os.path.abspath(__file__))
        self.log_max_size = log_max_size
        self.log_backup_count = log_backup_count

        # 初始化日志系统
        self._setup_logging()

        # 支持的优惠券类型
        self.categories = {
            "SMART_WEARABLES": {
                "name": "手环",
                "color": Fore.CYAN
            },
            # "WASHER": {
            #     "name": "洗衣机",
            #     "color": Fore.GREEN
            # },
            # "FRIDGE": {
            #     "name": "冰箱",
            #     "color": Fore.YELLOW
            # }
        }

        # 成功关键词
        self.success_keywords = ["领取成功", "成功领取", "抢券成功", "领券成功"]

        # 失败关键词（需要重试）
        self.retry_keywords = ["活动火爆", "领取失败", "资格已用完", "请稍后再试", "系统繁忙"]

        # 请求配置
        self.timeout = aiohttp.ClientTimeout(total=10)
        self.headers = {
            'Content-Type':
            'application/json',
            'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090819)XWEB/13639'
        }

        # 统计信息
        self.stats = {category: {"attempts": 0, "success": False} for category in self.categories}

        # 时间同步相关
        self.time_offset_ms = 0  # 本地时间与服务器时间的差值（毫秒）
        self.last_sync_time = None  # 最后一次同步时间
        self.sync_status = "未同步"  # 同步状态

    def _setup_logging(self):
        """
        设置日志系统，包括文件日志和控制台日志
        """
        try:
            # 确保日志目录存在
            os.makedirs(self.log_dir, exist_ok=True)

            # 生成日志文件名（按日期）
            current_date = datetime.now().strftime("%Y%m%d")
            log_filename = f"coupon_grabber_{current_date}.log"
            log_filepath = os.path.join(self.log_dir, log_filename)

            # 创建文件处理器（带轮转功能）
            self.file_handler = RotatingFileHandler(
                log_filepath, maxBytes=self.log_max_size, backupCount=self.log_backup_count, encoding='utf-8'
            )

            # 设置文件日志格式（不包含颜色代码）
            file_formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s', datefmt='%H:%M:%S')
            self.file_handler.setFormatter(file_formatter)
            self.file_handler.setLevel(logging.INFO)

            # 创建logger
            self.logger = logging.getLogger(f'coupon_grabber_{id(self)}')
            self.logger.setLevel(logging.INFO)

            # 清除已有的处理器（避免重复）
            self.logger.handlers.clear()

            # 添加文件处理器
            self.logger.addHandler(self.file_handler)

            # 防止日志传播到根logger
            self.logger.propagate = False

            # 记录日志系统初始化成功
            self.logger.info(f"日志系统初始化成功，日志文件：{log_filepath}")

        except Exception as e:
            # 如果日志系统初始化失败，创建一个空的logger以避免程序崩溃
            self.logger = logging.getLogger(f'coupon_grabber_fallback_{id(self)}')
            self.logger.addHandler(logging.NullHandler())
            print(f"警告：日志文件初始化失败：{str(e)}，将仅使用控制台输出")

    def generate_plat_ssn(self) -> str:
        """
        生成platSsn值 - 基于JavaScript代码分析的UUID v4算法
        
        JavaScript原始代码:
        function i() {
            const t = []
              , e = "0123456789abcdef";
            for (let i = 0; i < 36; i++)
                t[i] = e.substr(Math.floor(16 * Math.random()), 1);
            t[14] = "4",
            t[19] = e.substr(3 & t[19] | 8, 1),
            t[8] = t[13] = t[18] = t[23] = "";
            const s = t.join("");
            return s
        }
        
        Returns:
            32位十六进制字符串
        """
        # 十六进制字符集
        hex_chars = "0123456789abcdef"

        # 生成36位随机十六进制字符数组
        chars = []
        for _ in range(36):
            chars.append(random.choice(hex_chars))

        # 设置UUID v4标准位
        chars[14] = "4"  # 版本位

        # 设置变体位 (3 & chars[19] | 8)
        variant_char = hex_chars[3 & int(chars[19], 16) | 8]
        chars[19] = variant_char

        # 移除连字符位置（第8、13、18、23位设为空）
        chars[8] = chars[13] = chars[18] = chars[23] = ""

        # 连接成字符串
        plat_ssn = "".join(chars)

        return plat_ssn

    def log_message(self, category: str, message: str, level: str = "INFO"):
        """
        打印带颜色的日志消息并同时写入日志文件

        Args:
            category: 优惠券类型
            message: 日志消息
            level: 日志级别
        """
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        color = self.categories.get(category, {}).get("color", Fore.WHITE)
        category_name = self.categories.get(category, {}).get("name", category)

        level_colors = {"INFO": Fore.WHITE, "SUCCESS": Fore.GREEN, "ERROR": Fore.RED, "WARNING": Fore.YELLOW}
        level_color = level_colors.get(level, Fore.WHITE)

        # 控制台输出（带颜色）
        console_message = (
            f"{Fore.BLUE}[{timestamp}]{Style.RESET_ALL} "
            f"{color}[{category_name}]{Style.RESET_ALL} "
            f"{level_color}[{level}]{Style.RESET_ALL} {message}"
        )
        print(console_message)

        # 文件输出（不带颜色）
        try:
            file_message = f"[{category_name}] {message}"

            # 根据级别选择合适的日志方法
            if level == "ERROR":
                self.logger.error(file_message)
            elif level == "WARNING":
                self.logger.warning(file_message)
            elif level == "SUCCESS":
                self.logger.info(f"SUCCESS: {file_message}")
            else:
                self.logger.info(file_message)
        except Exception:
            # 如果文件日志写入失败，不影响程序继续运行
            pass

    def log_system_message(self, message: str, level: str = "INFO"):
        """
        记录系统级别的日志消息（不区分优惠券类型）

        Args:
            message: 日志消息
            level: 日志级别
        """
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        level_colors = {"INFO": Fore.WHITE, "SUCCESS": Fore.GREEN, "ERROR": Fore.RED, "WARNING": Fore.YELLOW}
        level_color = level_colors.get(level, Fore.WHITE)

        # 控制台输出（带颜色）
        console_message = (f"{Fore.BLUE}[{timestamp}]{Style.RESET_ALL} "
                           f"{level_color}[{level}]{Style.RESET_ALL} {message}")
        print(console_message)

        # 文件输出（不带颜色）
        try:
            # 根据级别选择合适的日志方法
            if level == "ERROR":
                self.logger.error(message)
            elif level == "WARNING":
                self.logger.warning(message)
            elif level == "SUCCESS":
                self.logger.info(f"SUCCESS: {message}")
            else:
                self.logger.info(message)
        except Exception:
            # 如果文件日志写入失败，不影响程序继续运行
            pass

    def close_logging(self):
        """
        关闭日志文件处理器，确保日志文件正确保存
        """
        try:
            if hasattr(self, 'file_handler') and self.file_handler:
                self.file_handler.close()
                self.logger.removeHandler(self.file_handler)
        except Exception:
            pass

    async def send_request(self, session: aiohttp.ClientSession, category: str) -> Dict:
        """
        发送单个优惠券领取请求
        
        Args:
            session: aiohttp会话
            category: 优惠券类型
            
        Returns:
            响应数据字典
        """
        plat_ssn = self.generate_plat_ssn()

        payload = {"platSsn": plat_ssn, "userInfo": self.user_info, "category": category}

        self.stats[category]["attempts"] += 1
        attempt_num = self.stats[category]["attempts"]

        self.log_message(category, f"第{attempt_num}次尝试 | platSsn: {plat_ssn[:8]}...{plat_ssn[-8:]}")

        try:
            async with session.post(self.base_url, json=payload, headers=self.headers, timeout=self.timeout) as response:

                status_code = response.status
                response_text = await response.text()

                self.log_message(category, f"响应状态码: {status_code}")
                self.log_message(category, f"响应内容: {response_text}")

                return {"status_code": status_code, "response_text": response_text, "success": False, "should_retry": True}

        except asyncio.TimeoutError:
            self.log_message(category, "请求超时", "WARNING")
            return {"status_code": 0, "response_text": "请求超时", "success": False, "should_retry": True}
        except Exception as e:
            self.log_message(category, f"请求异常: {str(e)}", "ERROR")
            return {"status_code": 0, "response_text": str(e), "success": False, "should_retry": True}

    def analyze_response(self, response_text: str) -> tuple[bool, bool]:
        """
        分析响应内容，判断是否成功和是否需要重试
        
        Args:
            response_text: 响应文本
            
        Returns:
            (是否成功, 是否需要重试)
        """
        # 检查成功关键词
        for keyword in self.success_keywords:
            if keyword in response_text:
                return True, False

        # 检查重试关键词
        for keyword in self.retry_keywords:
            if keyword in response_text:
                return False, True

        # 默认情况：失败但继续重试
        return False, True

    async def grab_category_coupon(self, session: aiohttp.ClientSession, category: str, max_attempts: int = 100):
        """
        为特定类型的优惠券持续尝试领取
        
        Args:
            session: aiohttp会话
            category: 优惠券类型
            max_attempts: 最大尝试次数
        """
        self.log_message(category, f"开始领取优惠券，最大尝试次数: {max_attempts}")

        while self.stats[category]["attempts"] < max_attempts and not self.stats[category]["success"]:

            result = await self.send_request(session, category)

            # 分析响应
            success, should_retry = self.analyze_response(result["response_text"])

            if success:
                self.stats[category]["success"] = True
                self.log_message(category, "🎉 优惠券领取成功！", "SUCCESS")
                break
            elif not should_retry:
                self.log_message(category, "收到明确失败响应，停止重试", "WARNING")
                break
            else:
                # 随机延迟，避免请求过于频繁
                delay = random.uniform(0.5, 2.0)
                await asyncio.sleep(delay)

        if not self.stats[category]["success"]:
            self.log_message(category, f"达到最大尝试次数({max_attempts})或收到停止信号", "WARNING")

    async def start_grabbing(self, max_attempts: int = 100):
        """
        开始并发领取所有类型的优惠券

        Args:
            max_attempts: 每个类型的最大尝试次数
        """
        # 记录启动信息到日志文件
        self.log_system_message("=" * 60)
        self.log_system_message("🚀 优惠券自动领取器启动")
        self.log_system_message(f"目标URL: {self.base_url}")
        self.log_system_message(f"支持类型: {', '.join([f'{cat}({info['name']})' for cat, info in self.categories.items()])}")
        self.log_system_message(f"最大尝试次数: {max_attempts}")
        self.log_system_message("=" * 60)

        # 保持原有的彩色控制台输出
        print(f"{Fore.MAGENTA}{'='*60}")
        print(f"{Fore.MAGENTA}🚀 优惠券自动领取器启动")
        print(f"{Fore.MAGENTA}目标URL: {self.base_url}")
        print(f"{Fore.MAGENTA}支持类型: {', '.join([f'{cat}({info['name']})' for cat, info in self.categories.items()])}")
        print(f"{Fore.MAGENTA}最大尝试次数: {max_attempts}")
        print(f"{Fore.MAGENTA}{'='*60}{Style.RESET_ALL}")

        async with aiohttp.ClientSession() as session:
            # 创建并发任务
            tasks = []
            for category in self.categories:
                task = asyncio.create_task(self.grab_category_coupon(session, category, max_attempts))
                tasks.append(task)

            # 等待所有任务完成
            await asyncio.gather(*tasks)

        # 打印最终统计
        self.print_final_stats()

    def print_final_stats(self):
        """打印最终统计信息"""
        # 记录统计信息到日志文件
        self.log_system_message("")
        self.log_system_message("=" * 60)
        self.log_system_message("📊 最终统计结果")
        self.log_system_message("=" * 60)

        for category, info in self.categories.items():
            stats = self.stats[category]
            name = info["name"]
            status_text = "成功" if stats["success"] else "失败"
            level = "SUCCESS" if stats["success"] else "WARNING"

            stats_message = f"[{name}] - 尝试次数: {stats['attempts']} - 状态: {status_text}"
            self.log_system_message(stats_message, level)

        # 保持原有的彩色控制台输出
        print(f"\n{Fore.MAGENTA}{'='*60}")
        print(f"{Fore.MAGENTA}📊 最终统计结果")
        print(f"{Fore.MAGENTA}{'='*60}{Style.RESET_ALL}")

        for category, info in self.categories.items():
            stats = self.stats[category]
            color = info["color"]
            name = info["name"]

            status = f"{Fore.GREEN}✅ 成功" if stats["success"] else f"{Fore.RED}❌ 失败"
            print(f"{color}[{name}]{Style.RESET_ALL} - 尝试次数: {stats['attempts']} - 状态: {status}{Style.RESET_ALL}")

        # 关闭日志文件
        self.close_logging()

    async def sync_server_time(self, session: aiohttp.ClientSession) -> bool:
        """
        同步服务器时间，计算时差

        Args:
            session: aiohttp会话

        Returns:
            是否同步成功
        """
        try:
            # 记录请求发送时间
            local_time_before = datetime.now()

            # 发送HEAD请求获取服务器时间（减少数据传输）
            async with session.head(self.base_url, headers=self.headers, timeout=self.timeout) as response:
                # 获取响应头中的Date字段
                date_header = response.headers.get('Date')
                if not date_header:
                    self.sync_status = "同步失败：无Date头"
                    return False

                # 记录响应接收时间
                local_time_after = datetime.now()

                # 解析服务器时间并转换为本地时区
                server_time = parsedate_to_datetime(date_header)
                # 将服务器时间转换为naive datetime（移除时区信息）
                server_time_naive = server_time.replace(tzinfo=None)

                # 计算网络延迟（往返时间的一半）
                network_delay = (local_time_after - local_time_before) / 2

                # 估算服务器时间（考虑网络延迟）
                estimated_server_time = server_time_naive + network_delay

                # 计算时差（毫秒）
                time_diff = (local_time_before - estimated_server_time).total_seconds() * 1000
                self.time_offset_ms = int(time_diff)

                self.last_sync_time = datetime.now()
                self.sync_status = "同步成功"

                return True

        except Exception as e:
            self.sync_status = f"同步失败：{str(e)}"
            return False

    def get_adjusted_target_time(self, target_time: datetime) -> datetime:
        """
        根据时差调整目标时间

        Args:
            target_time: 原始目标时间

        Returns:
            调整后的目标时间
        """
        # 如果本地时间快于服务器时间，需要提前发送请求
        adjustment_ms = -self.time_offset_ms
        adjustment_seconds = adjustment_ms / 1000.0

        return target_time + timedelta(seconds=adjustment_seconds)


class ScheduledCouponGrabber:
    """定时优惠券抢券器"""

    def __init__(
        self, user_info: str, target_time_str: str, log_dir: str = None, log_max_size: int = 10 * 1024 * 1024, log_backup_count: int = 5
    ):
        """
        初始化定时抢券器

        Args:
            user_info: 加密的用户信息字符串
            target_time_str: 目标时间字符串，格式：HH:MM:SS
            log_dir: 日志文件保存目录，默认为脚本所在目录
            log_max_size: 单个日志文件最大大小（字节），默认10MB
            log_backup_count: 日志文件备份数量，默认5个
        """
        self.grabber = CouponGrabber(user_info, log_dir, log_max_size, log_backup_count)
        self.target_time_str = target_time_str
        self.target_time = self._parse_target_time(target_time_str)
        self.sync_thread = None
        self.stop_sync = False

    def _parse_target_time(self, time_str: str) -> datetime:
        """
        解析目标时间字符串

        Args:
            time_str: 时间字符串，格式：HH:MM:SS

        Returns:
            今天的目标时间
        """
        try:
            time_parts = time_str.split(':')
            if len(time_parts) != 3:
                raise ValueError("时间格式错误")

            hour, minute, second = map(int, time_parts)

            # 获取今天的日期
            today = datetime.now().date()
            target_time = datetime.combine(today, datetime.min.time().replace(hour=hour, minute=minute, second=second))

            # 如果目标时间已经过了，设置为明天
            if target_time <= datetime.now():
                target_time += timedelta(days=1)

            return target_time

        except Exception as e:
            raise ValueError(f"无法解析时间格式 '{time_str}'：{str(e)}")

    def _start_time_sync(self):
        """启动时间同步线程"""

        async def sync_loop():
            async with aiohttp.ClientSession() as session:
                while not self.stop_sync:
                    await self.grabber.sync_server_time(session)
                    await asyncio.sleep(10)  # 每10秒同步一次

        def run_sync():
            asyncio.run(sync_loop())

        self.sync_thread = threading.Thread(target=run_sync, daemon=True)
        self.sync_thread.start()

    def _format_time_remaining(self, remaining_seconds: float) -> str:
        """
        格式化剩余时间显示

        Args:
            remaining_seconds: 剩余秒数

        Returns:
            格式化的时间字符串
        """
        if remaining_seconds <= 0:
            return "00:00:00.000"

        hours = int(remaining_seconds // 3600)
        minutes = int((remaining_seconds % 3600) // 60)
        seconds = remaining_seconds % 60

        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"

    async def start_scheduled_grabbing(self, max_attempts: int = 100):
        """
        开始定时抢券

        Args:
            max_attempts: 每个类型的最大尝试次数
        """
        # 记录定时抢券启动信息到日志文件
        self.grabber.log_system_message("=" * 80)
        self.grabber.log_system_message("🕐 定时优惠券抢券器启动")
        self.grabber.log_system_message(f"目标时间: {self.target_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.grabber.log_system_message(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.grabber.log_system_message("=" * 80)

        # 保持原有的彩色控制台输出
        print(f"{Fore.MAGENTA}{'='*80}")
        print(f"{Fore.MAGENTA}🕐 定时优惠券抢券器启动")
        print(f"{Fore.MAGENTA}目标时间: {self.target_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{Fore.MAGENTA}当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{Fore.MAGENTA}{'='*80}{Style.RESET_ALL}")

        # 启动时间同步
        self._start_time_sync()

        # 等待一秒让时间同步开始工作
        await asyncio.sleep(1)

        try:
            # 倒计时循环
            while True:
                current_time = datetime.now()
                remaining_seconds = (self.target_time - current_time).total_seconds()

                if remaining_seconds <= 0:
                    break

                # 清屏并显示倒计时信息
                print(f"\r{' ' * 100}", end='')  # 清除当前行

                time_remaining_str = self._format_time_remaining(remaining_seconds)
                offset_str = f"{self.grabber.time_offset_ms:+d}ms" if self.grabber.time_offset_ms != 0 else "0ms"

                status_line = (
                    f"\r{Fore.CYAN}⏰ 距离抢券还有: {Fore.YELLOW}{time_remaining_str}{Style.RESET_ALL} | "
                    f"{Fore.GREEN}时差: {offset_str}{Style.RESET_ALL} | "
                    f"{Fore.BLUE}同步状态: {self.grabber.sync_status}{Style.RESET_ALL}"
                )

                print(status_line, end='', flush=True)

                # 精确等待
                await asyncio.sleep(0.1)

            # 停止时间同步
            self.stop_sync = True

            # 记录抢券开始到日志文件
            self.grabber.log_system_message("🚀 时间到！开始抢券...")
            print(f"\n{Fore.RED}🚀 时间到！开始抢券...{Style.RESET_ALL}")

            # 根据时差调整后立即开始抢券
            adjusted_target = self.grabber.get_adjusted_target_time(self.target_time)
            current_time = datetime.now()

            if adjusted_target > current_time:
                wait_time = (adjusted_target - current_time).total_seconds()
                self.grabber.log_system_message(f"⏳ 根据时差调整，还需等待 {wait_time:.3f} 秒...")
                print(f"{Fore.YELLOW}⏳ 根据时差调整，还需等待 {wait_time:.3f} 秒...{Style.RESET_ALL}")
                await asyncio.sleep(wait_time)

            # 开始抢券
            await self.grabber.start_grabbing(max_attempts)

        except KeyboardInterrupt:
            self.grabber.log_system_message("⚠️ 用户中断，停止定时抢券", "WARNING")
            print(f"\n{Fore.YELLOW}⚠️ 用户中断，停止定时抢券{Style.RESET_ALL}")
            self.stop_sync = True


def parse_target_time(time_str: str) -> datetime:
    """
    解析目标时间字符串，返回今天的目标时间

    Args:
        time_str: 时间字符串，格式：HH:MM:SS

    Returns:
        今天的目标时间
    """
    try:
        time_parts = time_str.split(':')
        if len(time_parts) != 3:
            raise ValueError("时间格式错误")

        hour, minute, second = map(int, time_parts)

        # 获取今天的日期
        today = datetime.now().date()
        target_time = datetime.combine(today, datetime.min.time().replace(hour=hour, minute=minute, second=second))

        return target_time

    except Exception as e:
        raise ValueError(f"无法解析时间格式 '{time_str}'：{str(e)}")


async def main():
    """主函数"""
    # 配置参数（可通过修改这些变量来调整设置）
    DEFAULT_TARGET_TIME = "10:00:00"  # 默认抢券时间
    MAX_ATTEMPTS = 100  # 每个优惠券类型的最大尝试次数

    # TODO: 请在这里填入您的userInfo加密字符串
    # USER_INFO = "70B12D2A2FD86152EE969F8C4D80EBC21215AAF6C924E106B3509859F66D785CAC974A36992F15A87CB422891083DDDACEBF6EE87724766C02FBD758823B48D501D4CD40E31EE105C492F0A91989D642BE7C25B4C79D2CB0AD7F19A890E266758030D05D98BF01EE4786EEE31F263378ECE8C0A731913E744A3D69D08736AD544B345AE1BB5532FC65C6BC397F580633408402782CBEB4CB45CDF426244BEBDFA3534C074429252F407FE68B0375F978EAD68C7AC8AC42CD9AF6182B57F9B5226CA785D0C21031B228CEABCC25F32836172AAFEAEA856E2DCB8ED5CD2498FCDDAE794E335EC55F1BEF266B17B4E175846DFF02AEDC20EDD903143B5FB634347D"
    USER_INFO = "70B12D2A2FD86152EE969F8C4D80EBC21215AAF6C924E106B3509859F66D785CAC974A36992F15A87CB422891083DDDACEBF6EE87724766C02FBD758823B48D501D4CD40E31EE105C492F0A91989D642BE7C25B4C79D2CB0AD7F19A890E266758030D05D98BF01EE4786EEE31F263378ECE8C0A731913E744A3D69D08736AD544B345AE1BB5532FC65C6BC397F580633408402782CBEB4CB45CDF426244BEBDFA3534C074429252F407FE68B0375F978EAD68C7AC8AC42CD9AF6182B57F9B5226CA785D0C21031B228CEABCC25F32836172AAFEAEA856E2DCB8ED5CD2498FCDDAE794E335EC55F1BEF266B17B4E17584177BF77DFFDC00A9663A270D368FCC10"

    if USER_INFO == "YOUR_ENCRYPTED_USER_INFO_HERE":
        print(f"{Fore.RED}❌ 请先设置USER_INFO变量为您的加密用户信息字符串{Style.RESET_ALL}")
        return

    try:
        # 解析目标时间
        target_time = parse_target_time(DEFAULT_TARGET_TIME)
        current_time = datetime.now()

        print(f"{Fore.MAGENTA}{'='*80}")
        print(f"{Fore.MAGENTA}🎯 优惠券自动抢券器")
        print(f"{Fore.MAGENTA}目标时间: {target_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{Fore.MAGENTA}当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{Fore.MAGENTA}{'='*80}{Style.RESET_ALL}")

        # 智能时间判断逻辑
        if current_time >= target_time:
            # 当前时间已超过目标时间，立即开始抢券
            print(f"{Fore.YELLOW}⚡ 当前时间已超过目标时间，立即开始抢券！{Style.RESET_ALL}")
            grabber = CouponGrabber(USER_INFO)
            await grabber.start_grabbing(max_attempts=MAX_ATTEMPTS)
        else:
            # 当前时间未到目标时间，进入定时抢券模式
            time_diff = (target_time - current_time).total_seconds()
            print(f"{Fore.CYAN}⏰ 距离目标时间还有 {time_diff:.1f} 秒，进入定时抢券模式{Style.RESET_ALL}")

            # 创建定时抢券器
            scheduled_grabber = ScheduledCouponGrabber(USER_INFO, DEFAULT_TARGET_TIME)

            # 开始定时抢券
            await scheduled_grabber.start_scheduled_grabbing(MAX_ATTEMPTS)

    except ValueError as e:
        print(f"{Fore.RED}❌ 时间格式错误: {str(e)}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}� 请检查代码中的 DEFAULT_TARGET_TIME 变量格式是否正确（应为 HH:MM:SS）{Style.RESET_ALL}")
        return


if __name__ == "__main__":
    asyncio.run(main())
